{"name": "ruoyi", "version": "3.8.4", "description": "若依管理系统", "author": "若依", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "ox<PERSON>", "lint:fix": "oxlint --fix"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@iconify/vue": "^5.0.0", "@narutogis/map3d-dc-sdk": "1.0.2", "@turf/turf": "^7.2.0", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "clipboard": "^2.0.11", "echarts": "5.4.0", "element-plus": "2.2.21", "file-saver": "2.0.5", "fuse.js": "6.6.2", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "larksr_websdk": "^3.3.112", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "moment": "^2.29.4", "nprogress": "0.2.0", "pinia": "2.0.22", "qs": "^6.11.0", "scene-communication-plugin": "^1.0.2", "svg-pan-zoom": "^3.6.1", "uuid": "^11.1.0", "vue": "3.5.13", "vue-cropper": "1.0.3", "vue-router": "4.1.4", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@narutogis/vite-plugin-dc": "^1.0.0", "@vitejs/plugin-vue": "4.4.0", "@vue/compiler-sfc": "3.5.13", "fast-glob": "^3.3.3", "oxlint": "^1.6.0", "rollup-plugin-visualizer": "^6.0.3", "sass": "1.69.4", "unplugin-auto-import": "^19.3.0", "vite": "4.5.0", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}