.tech-panel {
  position: fixed;
  left: 20px;
  top: 100px;
  width: 360px;
  height: calc(100vh - 110px);
  z-index: 100;
  pointer-events: auto;
  color: #fff;
  transition: width 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
    transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  // 添加折叠状态样式
  &.collapsed {
    width: 30px;
    transform: translateX(-10px);

    .panel-container {
      opacity: 0;
      visibility: hidden;
      transform: translateX(-20px);
    }

    .collapse-button {
      right: 20px;
    }
  }

  .panel-container {
    width: 100%;
    height: 100%;
    background: #011c46b3;
    border: 1px solid rgba(45, 115, 193, 0.6);
    border-radius: 2px;
    padding: 12px;
    //   display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    overflow-x: hidden;
    transition: opacity 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
      visibility 0.3s cubic-bezier(0.25, 0.8, 0.25, 1),
      transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(13, 47, 102, 0.3);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(100, 181, 246, 0.5);
      border-radius: 2px;

      &:hover {
        background: rgba(100, 181, 246, 0.7);
      }
    }
  }

  // 折叠/展开按钮样式
  .collapse-button {
    position: absolute;
    top: 50%;
    right: -2px;
    transform: translateY(-50%);
    width: 22px;
    height: 38px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 10;
    overflow: hidden;

    &:hover {
      transform: translateY(-50%) scale(1.05);

      img {
        filter: brightness(1.2);
      }
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }

    img {
      width: 22px;
      height: 38px;
      transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1),
        filter 0.3s ease;
      transform: rotate(180deg);

      &.rotated {
        transform: rotate(0);
      }
    }
  }

  .panel-section {
    border-radius: 2px;
    padding: 8px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: rgba(17, 149, 255, 0.08);
    margin-bottom: 10px;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        rgba(45, 115, 193, 0) 0%,
        rgba(45, 115, 193, 0.3) 50%,
        rgba(45, 115, 193, 0) 100%
      );
    }

    &.top-section {
      height: 650px;
      // flex: 1;
    }

    &.middle-section {
      flex: 1;
    }

    &.bottom-section {
      height: 280px;
    }

    &.gate-safety-section {
      height: 380px;
    }

    // 天气信息样式
    .weather-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      height: 28px;

      .title {
        font-size: 16px;
        font-weight: 700;
        font-family: AlimamaShuHeiTi;
        color: #8cd2ff;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;

        :deep(.time-range-select) {
          width: 90px; // 设置固定宽度

          .el-input {
            .el-input__wrapper {
              background: transparent;
              box-shadow: none !important;
              padding: 0;
              height: 28px;

              &.is-focus {
                box-shadow: none !important;
              }

              .el-input__inner {
                color: #8cd2ff;
                font-size: 13px;
                height: 28px;
                line-height: 28px;
                padding: 0;
                border: none;
                background: transparent;
                text-align: center;

                &::placeholder {
                  color: rgba(100, 181, 246, 0.8);
                }
              }

              .el-select__caret {
                color: #8cd2ff;
                font-size: 12px;
                height: 28px;
                line-height: 28px;
                width: 12px;
              }
            }
          }
        }
      }
    }

    .line {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 2px;

      .left,
      .right {
        /* flex:0 0 100px; */
        width: 10px;
        height: 2px;
        background-color: #3276b1;
      }

      .center {
        flex: 1;
        height: 2px;
        background-color: #19477a;
      }
    }

    .weather-main {
      display: flex;
      margin-bottom: 0;
      padding: 0;
      height: 85px;

      .current-weather {
        width: 110px;
        text-align: center;
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .rain-info {
          z-index: 2;
          text-align: left;
        }
      }

      .rain-label {
        color: rgba(255, 255, 255, 0.85);
        margin-bottom: 2px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        // font-size: 10px;
        font-size: 12px;
      }

      .rain-value {
        margin-top: 0;

        .value {
          font-size: 20px;
          font-weight: bold;
          color: #29b6f6;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.5px;
        }

        .unit {
          font-size: 12px;
          margin-left: 2px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 300;
        }
      }

      .update-time {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        margin-top: 4px;
        display: flex;
        align-items: center;

        .update-icon {
          width: 10px;
          height: 10px;
          margin-right: 3px;
        }

        span {
          transform: translateY(1px);
        }
      }

      .day-forecast {
        flex: 1;
        display: flex;
        justify-content: space-around;
        padding-left: 10px;
        gap: 4px;

        .forecast-item {
          flex: 1;
          text-align: center;
          padding: 3px 1px;
          position: relative;
          z-index: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          transition: all 0.3s ease;

          &:not(:last-child)::after {
            content: "";
            position: absolute;
            right: -2px;
            top: 20%;
            height: 60%;
            width: 1px;
            background: rgba(255, 255, 255, 0.15);
          }

          &:hover {
            transform: translateY(-2px);

            .weather-icon-img {
              transform: scale(1.1);
            }
          }

          .day-badge {
            font-size: 9px;
            font-weight: 600;
            color: #fff;
            background: rgba(21, 101, 192, 0.3);
            border-radius: 8px;
            padding: 1px 6px;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            margin-bottom: 4px;
          }

          .weather-icon-container {
            position: relative;
            width: 28px;
            height: 28px;
            margin: 2px auto;

            .weather-icon-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              position: relative;
              z-index: 1;
              filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
              transition: transform 0.3s ease;
            }
          }

          .temp {
            font-size: 14px;
            font-weight: 700;
            color: #29b6f6;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            margin-top: 2px;
          }
        }
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      position: relative;
      height: 28px;

      .title {
        font-size: 16px;
        font-weight: 700;
        font-family: AlimamaShuHeiTi;
        color: #8cd2ff;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;

        :deep(.time-range-select) {
          width: 90px; // 设置固定宽度

          .el-input {
            .el-input__wrapper {
              background: transparent;
              box-shadow: none !important;
              padding: 0;
              height: 28px;

              &.is-focus {
                box-shadow: none !important;
              }

              .el-input__inner {
                color: #8cd2ff;
                font-size: 13px;
                height: 28px;
                line-height: 28px;
                padding: 0;
                border: none;
                background: transparent;
                text-align: center;

                &::placeholder {
                  color: rgba(100, 181, 246, 0.8);
                }
              }

              .el-select__caret {
                color: #8cd2ff;
                font-size: 12px;
                height: 28px;
                line-height: 28px;
                width: 12px;
              }
            }
          }
        }
      }

      .location-info {
        display: flex;
        align-items: center;
        gap: 4px;
        height: 28px;
      }
    }

    .section-content {
      position: relative;
      flex: 1;
      overflow: hidden;

      .chart-container {
        height: 100%;
        width: 100%;
        margin-top: 0;
      }

      .river-chart-container {
        height: 200px;
        min-height: 200px;
        margin-top: 10px;
        width: 100%;
      }

      .gate-safety-container {
        height: 100%;
        width: 100%;
      }

      // 现代化球状仪表盘布局
      .modern-gate-layout {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        padding: 5px 0;
      }

      .gate-row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        gap: 8px;

        &.first-row {
          flex: 1;
          justify-content: space-around;
          padding: 0 8px;
        }

        &.second-row {
          flex: 1;
          justify-content: center;
          gap: 45px;
          padding: 0 25px;
        }
      }

      .modern-gate-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        cursor: pointer;
        position: relative;
      }

      .gate-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }

      // 现代化朴素的圆形仪表盘
      .modern-dashboard {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        position: relative;
        overflow: hidden;
        background: linear-gradient(
          135deg,
          rgba(15, 30, 60, 0.95) 0%,
          rgba(20, 50, 90, 0.9) 40%,
          rgba(25, 65, 120, 0.85) 100%
        );
        border: 1px solid rgba(100, 181, 246, 0.25);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25), 0 1px 3px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.08),
          inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);

        // 精致的高光效果
        &::before {
          content: "";
          position: absolute;
          top: 8%;
          left: 12%;
          width: 35%;
          height: 35%;
          border-radius: 50%;
          background: radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.08) 40%,
            transparent 70%
          );
          z-index: 1;
          animation: subtle-shimmer 4s ease-in-out infinite;
        }

        // 悬停时的增强效果
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
            0 2px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(100, 181, 246, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
        }
      }

      // 悬停时显示详情
      .modern-gate-item:hover .hover-details {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
      }
    }

    // 现代化进度环
    .dashboard-ring {
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 50%;
      z-index: -1;
    }

    .ring-progress {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      position: relative;
      background: conic-gradient(
        from -90deg,
        var(--ring-color, #64b5f6) 0deg,
        var(--ring-color, #64b5f6) calc(var(--progress, 0%) * 3.6deg),
        rgba(255, 255, 255, 0.08) calc(var(--progress, 0%) * 3.6deg),
        rgba(255, 255, 255, 0.08) 360deg
      );
      filter: drop-shadow(0 0 6px rgba(100, 181, 246, 0.3));

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 88%;
        height: 88%;
        transform: translate(-50%, -50%);
        background: radial-gradient(
            circle at 35% 25%,
            rgba(120, 200, 255, 0.08) 0%,
            transparent 50%
          ),
          linear-gradient(
            135deg,
            rgba(15, 30, 60, 0.95) 0%,
            rgba(12, 25, 50, 0.98) 100%
          );
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 0.05);
      }

      &::after {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        bottom: 1px;
        background: conic-gradient(
          from -90deg,
          rgba(255, 255, 255, 0.2) 0deg,
          rgba(255, 255, 255, 0.2) calc(var(--progress, 0%) * 3.6deg),
          transparent calc(var(--progress, 0%) * 3.6deg)
        );
        border-radius: 50%;
        animation: modern-ring-pulse 4s ease-in-out infinite;
        opacity: 0.5;
      }
    }

    // 水位容器
    .water-container {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      top: 0;
      border-radius: 50%;
      overflow: hidden;
    }

    .water-fill {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      border-radius: 0 0 50% 50%;
      transition: height 1s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;

      // 添加水面扰动效果
      &::before {
        content: "";
        position: absolute;
        top: -20px;
        left: -20%;
        width: 140%;
        height: 25px;
        background: radial-gradient(
          ellipse at center,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 30%,
          transparent 70%
        );
        animation: water-disturbance 4s ease-in-out infinite;
        z-index: 2;
      }

      &.risk-low {
        background: linear-gradient(
          to top,
          #4caf50 0%,
          #66bb6a 25%,
          #81c784 50%,
          #a5d6a7 75%,
          #c8e6c9 100%
        );

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 20%,
            transparent 40%,
            rgba(255, 255, 255, 0.1) 60%,
            transparent 80%
          );
          animation: water-light-play 6s ease-in-out infinite;
        }
      }

      &.risk-medium {
        background: linear-gradient(
          to top,
          #ff9800 0%,
          #ffb74d 25%,
          #ffcc80 50%,
          #ffe0b2 75%,
          #fff3e0 100%
        );

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 20%,
            transparent 40%,
            rgba(255, 255, 255, 0.1) 60%,
            transparent 80%
          );
          animation: water-light-play 6s ease-in-out infinite;
        }
      }

      &.risk-high {
        background: linear-gradient(
          to top,
          #ff5722 0%,
          #ff7043 25%,
          #ff8a65 50%,
          #ffab91 75%,
          #ffccbc 100%
        );

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 20%,
            transparent 40%,
            rgba(255, 255, 255, 0.1) 60%,
            transparent 80%
          );
          animation: water-light-play 6s ease-in-out infinite;
        }
      }

      &.risk-danger {
        background: linear-gradient(
          to top,
          #f44336 0%,
          #ef5350 25%,
          #e57373 50%,
          #ef9a9a 75%,
          #ffcdd2 100%
        );

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 20%,
            transparent 40%,
            rgba(255, 255, 255, 0.1) 60%,
            transparent 80%
          );
          animation: water-light-play 5s ease-in-out infinite,
            water-agitation 2s ease-in-out infinite;
        }
      }
    }

    // 增强的水波纹效果
    .water-surface-modern {
      position: absolute;
      top: -12px;
      left: 0;
      right: 0;
      height: 24px;
      overflow: hidden;
      z-index: 10;
    }

    .wave {
      position: absolute;
      top: 0;
      left: -100%;
      width: 200%;

      &.wave-1 {
        height: 14px;
        top: 0;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.6) 15%,
          rgba(255, 255, 255, 0.85) 30%,
          rgba(255, 255, 255, 0.95) 50%,
          rgba(255, 255, 255, 0.85) 70%,
          rgba(255, 255, 255, 0.6) 85%,
          transparent 100%
        );
        border-radius: 50%;
        animation: enhanced-wave-flow-1 2.8s ease-in-out infinite;
        z-index: 5;
        filter: blur(0.5px);
      }

      &.wave-2 {
        height: 12px;
        top: 4px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.45) 20%,
          rgba(255, 255, 255, 0.75) 40%,
          rgba(255, 255, 255, 0.9) 50%,
          rgba(255, 255, 255, 0.75) 60%,
          rgba(255, 255, 255, 0.45) 80%,
          transparent 100%
        );
        border-radius: 50%;
        animation: enhanced-wave-flow-2 3.2s ease-in-out infinite reverse;
        z-index: 4;
        filter: blur(0.3px);
      }
    }

    // 第三层波浪 - 显示并增强
    .wave-3 {
      position: absolute;
      top: 8px;
      left: -80%;
      width: 160%;
      height: 10px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.35) 25%,
        rgba(255, 255, 255, 0.65) 50%,
        rgba(255, 255, 255, 0.35) 75%,
        transparent 100%
      );
      border-radius: 50%;
      animation: enhanced-wave-flow-3 3.8s ease-in-out infinite;
      z-index: 3;
    }

    // 显示第四层和第五层波浪以增强效果
    .wave-4 {
      position: absolute;
      top: 2px;
      left: -120%;
      width: 240%;
      height: 8px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.25) 30%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0.25) 70%,
        transparent 100%
      );
      border-radius: 50%;
      animation: enhanced-wave-flow-4 4.2s ease-in-out infinite;
      z-index: 2;
      filter: blur(1px);
    }

    .wave-5 {
      position: absolute;
      top: 10px;
      left: -60%;
      width: 120%;
      height: 6px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 35%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0.2) 65%,
        transparent 100%
      );
      border-radius: 50%;
      animation: enhanced-wave-flow-5 2.5s ease-in-out infinite reverse;
      z-index: 1;
    }

    // 增强的水面反光效果
    .water-reflection {
      position: absolute;
      top: -4px;
      left: 10%;
      width: 80%;
      height: 8px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 20%,
        rgba(255, 255, 255, 0.75) 35%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 0.75) 65%,
        rgba(255, 255, 255, 0.4) 80%,
        transparent 100%
      );
      border-radius: 50%;
      animation: enhanced-water-shimmer 2.5s ease-in-out infinite alternate;
      z-index: 6;
      filter: blur(0.5px);
    }

    // 增强的涟漪效果
    .water-ripples-surface {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 8;

      .ripple-circle {
        position: absolute;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: water-ripple-expand 3s ease-out infinite;

        &:nth-child(1) {
          top: 20%;
          left: 30%;
          width: 8px;
          height: 8px;
          animation-delay: 0s;
        }

        &:nth-child(2) {
          top: 40%;
          left: 60%;
          width: 6px;
          height: 6px;
          animation-delay: 1s;
        }

        &:nth-child(3) {
          top: 60%;
          left: 25%;
          width: 10px;
          height: 10px;
          animation-delay: 2s;
        }

        &:nth-child(4) {
          top: 30%;
          left: 70%;
          width: 4px;
          height: 4px;
          animation-delay: 0.5s;
        }
      }
    }

    // 中心内容显示
    .dashboard-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 10;
    }

    .percentage-display {
      color: #ffffff;
      font-size: 17px;
      font-weight: 700;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7),
        0 0 8px rgba(100, 181, 246, 0.4);
      letter-spacing: 0.3px;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      margin-bottom: 3px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui,
        sans-serif;
    }

    .status-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 8px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.4px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui,
        sans-serif;

      &.risk-low {
        background: linear-gradient(
          135deg,
          rgba(76, 175, 80, 0.85),
          rgba(139, 195, 74, 0.75)
        );
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        border-color: rgba(76, 175, 80, 0.3);
      }

      &.risk-medium {
        background: linear-gradient(
          135deg,
          rgba(255, 152, 0, 0.85),
          rgba(255, 193, 7, 0.75)
        );
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        border-color: rgba(255, 152, 0, 0.3);
      }

      &.risk-high {
        background: linear-gradient(
          135deg,
          rgba(255, 87, 34, 0.85),
          rgba(255, 111, 97, 0.75)
        );
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        border-color: rgba(255, 87, 34, 0.3);
      }

      &.risk-danger {
        background: linear-gradient(
          135deg,
          rgba(244, 67, 54, 0.9),
          rgba(229, 57, 53, 0.8)
        );
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        border-color: rgba(244, 67, 54, 0.4);
        animation: modern-danger-pulse 2.5s ease-in-out infinite;
      }
    }

    .modern-gate-name {
      color: rgba(255, 255, 255, 0.92);
      font-size: 11px;
      font-weight: 500;
      text-align: center;
      margin-top: 8px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      letter-spacing: 0.2px;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      padding: 4px 8px;
      border-radius: 8px;
      background: rgba(15, 30, 60, 0.25);
      backdrop-filter: blur(6px);
      border: 1px solid rgba(100, 181, 246, 0.12);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui,
        sans-serif;
    }

    // 现代化悬浮详情
    .hover-details {
      position: absolute;
      top: 105%;
      left: 50%;
      transform: translateX(-50%) translateY(6px);
      background: rgba(8, 20, 45, 0.96);
      border: 1px solid rgba(64, 158, 255, 0.25);
      border-radius: 8px;
      padding: 8px 10px;
      min-width: 120px;
      backdrop-filter: blur(15px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.35), 0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      opacity: 0;
      visibility: hidden;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      z-index: 100;

      &::before {
        content: "";
        position: absolute;
        top: -6px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid rgba(8, 20, 45, 0.96);
      }
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 9px;
        color: rgba(255, 255, 255, 0.75);
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui,
          sans-serif;
      }

      .value {
        font-size: 9px;
        color: #64b5f6;
        font-weight: 600;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui,
          sans-serif;

        &.safety-margin {
          color: #66bb6a;
        }
      }
    }

    // 现代化动画关键帧
    @keyframes subtle-shimmer {
      0% {
        opacity: 0.1;
        transform: scale(1) rotate(0deg);
      }
      50% {
        opacity: 0.15;
        transform: scale(1.05) rotate(2deg);
      }
      100% {
        opacity: 0.1;
        transform: scale(1) rotate(0deg);
      }
    }

    @keyframes modern-ring-pulse {
      0% {
        opacity: 0.3;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.01);
      }
      100% {
        opacity: 0.3;
        transform: scale(1);
      }
    }

    // 增强的水波动画
    @keyframes enhanced-wave-flow-1 {
      0% {
        transform: translateX(0) scaleY(1);
        opacity: 0.8;
      }
      20% {
        transform: translateX(20px) scaleY(1.3);
        opacity: 0.95;
      }
      40% {
        transform: translateX(40px) scaleY(0.7);
        opacity: 0.85;
      }
      60% {
        transform: translateX(60px) scaleY(1.2);
        opacity: 0.9;
      }
      80% {
        transform: translateX(80px) scaleY(0.9);
        opacity: 0.85;
      }
      100% {
        transform: translateX(100px) scaleY(1);
        opacity: 0.8;
      }
    }

    @keyframes enhanced-wave-flow-2 {
      0% {
        transform: translateX(0) scaleY(0.8);
        opacity: 0.7;
      }
      25% {
        transform: translateX(-25px) scaleY(1.4);
        opacity: 0.9;
      }
      50% {
        transform: translateX(-50px) scaleY(0.6);
        opacity: 0.75;
      }
      75% {
        transform: translateX(-75px) scaleY(1.1);
        opacity: 0.85;
      }
      100% {
        transform: translateX(-100px) scaleY(0.8);
        opacity: 0.7;
      }
    }

    @keyframes enhanced-wave-flow-3 {
      0% {
        transform: translateX(0) scaleY(1);
        opacity: 0.6;
      }
      30% {
        transform: translateX(18px) scaleY(1.25);
        opacity: 0.8;
      }
      60% {
        transform: translateX(36px) scaleY(0.75);
        opacity: 0.7;
      }
      100% {
        transform: translateX(54px) scaleY(1);
        opacity: 0.6;
      }
    }

    @keyframes enhanced-wave-flow-4 {
      0% {
        transform: translateX(0) scaleY(0.9);
        opacity: 0.5;
      }
      35% {
        transform: translateX(-35px) scaleY(1.2);
        opacity: 0.7;
      }
      70% {
        transform: translateX(-70px) scaleY(0.8);
        opacity: 0.6;
      }
      100% {
        transform: translateX(-105px) scaleY(0.9);
        opacity: 0.5;
      }
    }

    @keyframes enhanced-wave-flow-5 {
      0% {
        transform: translateX(0) scaleY(1.1);
        opacity: 0.4;
      }
      40% {
        transform: translateX(24px) scaleY(1.3);
        opacity: 0.6;
      }
      80% {
        transform: translateX(48px) scaleY(0.9);
        opacity: 0.5;
      }
      100% {
        transform: translateX(60px) scaleY(1.1);
        opacity: 0.4;
      }
    }

    @keyframes enhanced-water-shimmer {
      0% {
        opacity: 0.6;
        transform: scaleX(0.8) scaleY(1);
      }
      30% {
        opacity: 0.9;
        transform: scaleX(1.3) scaleY(1.15);
      }
      60% {
        opacity: 0.75;
        transform: scaleX(0.95) scaleY(0.85);
      }
      100% {
        opacity: 0.6;
        transform: scaleX(0.8) scaleY(1);
      }
    }

    @keyframes water-ripple-expand {
      0% {
        transform: scale(0);
        opacity: 0.8;
      }
      30% {
        transform: scale(1);
        opacity: 0.6;
      }
      60% {
        transform: scale(2);
        opacity: 0.3;
      }
      100% {
        transform: scale(3);
        opacity: 0;
      }
    }

    @keyframes modern-danger-pulse {
      0%,
      100% {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
      }
      50% {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3),
          0 0 15px rgba(244, 67, 54, 0.7);
      }
    }

    @keyframes status-pulse {
      0%,
      100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.1);
      }
    }

    @keyframes danger-blink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    @keyframes water-disturbance {
      0%,
      100% {
        transform: translateY(0) scaleX(1);
        opacity: 0.3;
      }
      25% {
        transform: translateY(-3px) scaleX(1.1);
        opacity: 0.6;
      }
      50% {
        transform: translateY(-5px) scaleX(0.9);
        opacity: 0.4;
      }
      75% {
        transform: translateY(-2px) scaleX(1.05);
        opacity: 0.7;
      }
    }

    @keyframes water-light-play {
      0% {
        transform: translateX(-100%) rotate(0deg);
        opacity: 0;
      }
      25% {
        opacity: 0.6;
      }
      50% {
        transform: translateX(0%) rotate(5deg);
        opacity: 0.8;
      }
      75% {
        opacity: 0.4;
      }
      100% {
        transform: translateX(100%) rotate(10deg);
        opacity: 0;
      }
    }

    @keyframes water-agitation {
      0%,
      100% {
        transform: scaleY(1) scaleX(1);
      }
      25% {
        transform: scaleY(1.02) scaleX(0.98);
      }
      50% {
        transform: scaleY(0.98) scaleX(1.02);
      }
      75% {
        transform: scaleY(1.01) scaleX(0.99);
      }
    }

    .update-time-bottom {
      text-align: center;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      margin-top: 5px;
      padding-bottom: 5px;
    }
  }

  // 统一天气卡片样式
  .unified-weather-card {
    background: linear-gradient(
      135deg,
      rgba(21, 101, 192, 0.2),
      rgba(3, 169, 244, 0.05)
    );
    border-radius: 12px;
    margin: 6px 0;
    padding: 8px 10px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 -1px 3px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(100, 181, 246, 0.3);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
  }
}

// Element Plus表格样式覆盖
:deep(.rain-data-table) {
  --el-table-header-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-header-text-color: #fff;
  --el-table-text-color: #fff;

  background-color: transparent;
  color: #fff;
  height: 100%;
  width: 100%;

  .el-table__header {
    background-color: transparent;

    th.el-table__cell {
      background-color: rgba(25, 118, 210, 0.4) !important;
      border-bottom: 1px solid rgba(45, 115, 193, 0.3);
      color: rgba(255, 255, 255, 0.95);
      font-size: 12px;

      .cell {
        color: rgba(255, 255, 255, 0.95);
      }
    }
  }

  .el-table__body {
    background-color: transparent;

    tr {
      background-color: transparent;

      td {
        background-color: transparent;
        color: #fff;

        .cell {
          color: #fff;
        }
      }
    }
  }

  &::before {
    display: none;
  }

  .el-table__inner-wrapper::before {
    display: none;
  }

  .el-table__header-wrapper {
    background-color: transparent;

    th.el-table__cell {
      background-color: rgba(25, 118, 210, 0.4) !important;
      border-bottom: 1px solid rgba(45, 115, 193, 0.3);

      &.is-leaf {
        border-bottom: 1px solid rgba(45, 115, 193, 0.3);
      }
    }
  }

  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(33, 150, 243, 0.5);
      border-radius: 2px;
    }

    tr.rain-table-row:hover > td.el-table__cell {
      background-color: rgba(21, 101, 192, 0.1);
    }

    td.el-table__cell {
      background-color: transparent !important;
      border-bottom: 1px solid rgba(45, 115, 193, 0.2);
    }
  }
}

// 保留原有的排名样式
.rank-item {
  width: 24px;
  height: 18px;
  line-height: 18px;
  margin: 0 auto;
  transform: skew(-15deg);
  position: relative;
  overflow: hidden;

  &.rank-1 {
    background: linear-gradient(45deg, #ff4757, #ff6b81);
    color: #fff;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
  }

  &.rank-2 {
    background: linear-gradient(45deg, #ffa502, #ffb733);
    color: #fff;
    box-shadow: 0 2px 4px rgba(255, 165, 2, 0.3);
  }

  &.rank-3 {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    color: #fff;
    box-shadow: 0 2px 4px rgba(46, 213, 115, 0.3);
  }
}

.empty-data-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  height: 100%;
  width: 100%;

  .empty-icon {
    font-size: 40px;
    opacity: 0.9;
    margin-bottom: 12px;

    .el-icon {
      width: 48px;
      height: 48px;
      font-size: 48px;
      color: rgba(100, 181, 246, 0.6);
      transition: color 0.3s ease;
    }
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
    color: rgba(200, 220, 255, 0.6);
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: color 0.3s ease;
    text-align: center;
    max-width: 80%;
  }
}

:deep(.basin-select),
:deep(.river-select) {
  width: 80px; // 设置固定宽度

  .el-input {
    .el-input__wrapper {
      background: transparent;
      box-shadow: none !important;
      padding: 0;
      height: 28px;

      &.is-focus {
        box-shadow: none !important;
      }

      .el-input__inner {
        color: #8cd2ff;
        font-size: 13px;
        height: 28px;
        line-height: 28px;
        padding: 0;
        border: none;
        background: transparent;
        text-align: center;

        &::placeholder {
          color: rgba(100, 181, 246, 0.8);
        }
      }

      .el-select__caret {
        color: #8cd2ff;
        font-size: 12px;
        height: 28px;
        line-height: 28px;
        width: 12px;
      }

      &:hover {
        background: rgba(21, 101, 192, 0.1);
      }

      &.is-focus {
        background: rgba(21, 101, 192, 0.15);
      }
    }
  }
}
